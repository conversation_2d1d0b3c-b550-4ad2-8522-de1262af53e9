2025-08-07 17:11:01,002 - __main__ - INFO - [START] Starting AI Trading Bot System...
2025-08-07 17:11:01,002 - __main__ - INFO - Version: 1.0.0
2025-08-07 17:11:01,002 - __main__ - INFO - Environment: development
2025-08-07 17:11:01,002 - __main__ - INFO - Paper Trading: True
2025-08-07 17:11:01,002 - src.monitoring.health_checker - INFO - Starting Health Checker...
2025-08-07 17:11:01,007 - src.core.trading_engine - INFO - Initializing Trading Engine components...
2025-08-07 17:11:01,091 - src.config.security - INFO - Encryption system initialized
2025-08-07 17:11:01,174 - src.exchanges.exchange_manager - INFO - Initializing Exchange Manager...
2025-08-07 17:11:02,516 - src.exchanges.exchange_manager - WARNING - Could not sync time with Binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:02,517 - src.exchanges.exchange_manager - INFO - [OK] Binance exchange configured
2025-08-07 17:11:02,519 - src.exchanges.exchange_manager - INFO - [OK] Kraken exchange configured
2025-08-07 17:11:02,519 - src.exchanges.exchange_manager - INFO - Configured exchanges: ['binance', 'kraken']
2025-08-07 17:11:04,075 - src.data.market_data_collector - INFO - Initializing Market Data Collector...
2025-08-07 17:11:04,076 - src.config.database - INFO - Initializing database connection...
2025-08-07 17:11:04,122 - src.config.database - INFO - Creating database tables...
2025-08-07 17:11:04,188 - src.data.market_data_collector - INFO - Added trading pair: BTC/USDT on kraken
2025-08-07 17:11:04,191 - src.data.market_data_collector - INFO - Added trading pair: ETH/USDT on kraken
2025-08-07 17:11:04,194 - src.data.market_data_collector - INFO - Added trading pair: BNB/USDT on binance
2025-08-07 17:11:04,196 - src.data.market_data_collector - INFO - Added trading pair: BNB/USDT on kraken
2025-08-07 17:11:04,198 - src.data.market_data_collector - INFO - Added trading pair: XRP/USDT on binance
2025-08-07 17:11:04,200 - src.data.market_data_collector - INFO - Added trading pair: XRP/USDT on kraken
2025-08-07 17:11:04,203 - src.data.market_data_collector - INFO - Added trading pair: ADA/USDT on kraken
2025-08-07 17:11:04,205 - src.data.market_data_collector - INFO - Added trading pair: SOL/USDT on binance
2025-08-07 17:11:04,207 - src.data.market_data_collector - INFO - Added trading pair: SOL/USDT on kraken
2025-08-07 17:11:04,209 - src.data.market_data_collector - INFO - Added trading pair: DOGE/USDT on binance
2025-08-07 17:11:04,211 - src.data.market_data_collector - INFO - Added trading pair: DOGE/USDT on kraken
2025-08-07 17:11:04,213 - src.data.market_data_collector - INFO - Added trading pair: DOT/USDT on kraken
2025-08-07 17:11:04,216 - src.data.market_data_collector - INFO - Added trading pair: MATIC/USDT on binance
2025-08-07 17:11:04,218 - src.data.market_data_collector - INFO - Added trading pair: MATIC/USDT on kraken
2025-08-07 17:11:04,220 - src.data.market_data_collector - INFO - Added trading pair: AVAX/USDT on binance
2025-08-07 17:11:04,222 - src.data.market_data_collector - INFO - Added trading pair: AVAX/USDT on kraken
2025-08-07 17:11:04,224 - src.data.market_data_collector - INFO - Added trading pair: UNI/USDT on binance
2025-08-07 17:11:04,225 - src.data.market_data_collector - INFO - Added trading pair: UNI/USDT on kraken
2025-08-07 17:11:04,228 - src.data.market_data_collector - INFO - Added trading pair: LINK/USDT on kraken
2025-08-07 17:11:04,230 - src.data.market_data_collector - INFO - Added trading pair: AAVE/USDT on binance
2025-08-07 17:11:04,232 - src.data.market_data_collector - INFO - Added trading pair: AAVE/USDT on kraken
2025-08-07 17:11:04,234 - src.data.market_data_collector - INFO - Added trading pair: SUSHI/USDT on binance
2025-08-07 17:11:04,235 - src.data.market_data_collector - INFO - Added trading pair: SUSHI/USDT on kraken
2025-08-07 17:11:04,237 - src.data.market_data_collector - INFO - Added trading pair: COMP/USDT on binance
2025-08-07 17:11:04,239 - src.data.market_data_collector - INFO - Added trading pair: COMP/USDT on kraken
2025-08-07 17:11:04,241 - src.data.market_data_collector - INFO - Added trading pair: ATOM/USDT on binance
2025-08-07 17:11:04,242 - src.data.market_data_collector - INFO - Added trading pair: ATOM/USDT on kraken
2025-08-07 17:11:04,245 - src.data.market_data_collector - INFO - Added trading pair: ALGO/USDT on binance
2025-08-07 17:11:04,247 - src.data.market_data_collector - INFO - Added trading pair: ALGO/USDT on kraken
2025-08-07 17:11:04,249 - src.data.market_data_collector - INFO - Added trading pair: NEAR/USDT on binance
2025-08-07 17:11:04,250 - src.data.market_data_collector - INFO - Added trading pair: NEAR/USDT on kraken
2025-08-07 17:11:04,252 - src.data.market_data_collector - INFO - Added trading pair: FTM/USDT on binance
2025-08-07 17:11:04,255 - src.data.market_data_collector - INFO - Added trading pair: FTM/USDT on kraken
2025-08-07 17:11:04,257 - src.data.market_data_collector - INFO - Added trading pair: ONE/USDT on binance
2025-08-07 17:11:04,259 - src.data.market_data_collector - INFO - Added trading pair: ONE/USDT on kraken
2025-08-07 17:11:04,261 - src.data.market_data_collector - INFO - Added trading pair: LTC/USDT on binance
2025-08-07 17:11:04,263 - src.data.market_data_collector - INFO - Added trading pair: LTC/USDT on kraken
2025-08-07 17:11:04,264 - src.data.market_data_collector - INFO - Added trading pair: BCH/USDT on binance
2025-08-07 17:11:04,266 - src.data.market_data_collector - INFO - Added trading pair: BCH/USDT on kraken
2025-08-07 17:11:04,268 - src.data.market_data_collector - INFO - Added trading pair: ETC/USDT on binance
2025-08-07 17:11:04,269 - src.data.market_data_collector - INFO - Added trading pair: ETC/USDT on kraken
2025-08-07 17:11:04,272 - src.data.market_data_collector - INFO - Added trading pair: XLM/USDT on binance
2025-08-07 17:11:04,273 - src.data.market_data_collector - INFO - Added trading pair: XLM/USDT on kraken
2025-08-07 17:11:04,275 - src.data.market_data_collector - INFO - Added trading pair: VET/USDT on binance
2025-08-07 17:11:04,276 - src.data.market_data_collector - INFO - Added trading pair: VET/USDT on kraken
2025-08-07 17:11:04,279 - src.data.market_data_collector - INFO - Added trading pair: THETA/USDT on binance
2025-08-07 17:11:04,280 - src.data.market_data_collector - INFO - Added trading pair: THETA/USDT on kraken
2025-08-07 17:11:04,282 - src.data.market_data_collector - INFO - Added trading pair: FIL/USDT on binance
2025-08-07 17:11:04,284 - src.data.market_data_collector - INFO - Added trading pair: FIL/USDT on kraken
2025-08-07 17:11:04,285 - src.data.market_data_collector - INFO - Added trading pair: TRX/USDT on binance
2025-08-07 17:11:04,288 - src.data.market_data_collector - INFO - Added trading pair: TRX/USDT on kraken
2025-08-07 17:11:04,289 - src.data.market_data_collector - INFO - Added trading pair: EOS/USDT on binance
2025-08-07 17:11:04,291 - src.data.market_data_collector - INFO - Added trading pair: EOS/USDT on kraken
2025-08-07 17:11:04,294 - src.data.market_data_collector - INFO - Added trading pair: IOTA/USDT on binance
2025-08-07 17:11:04,295 - src.data.market_data_collector - INFO - Added trading pair: IOTA/USDT on kraken
2025-08-07 17:11:04,305 - src.data.market_data_collector - ERROR - Failed to ensure trading pairs: (psycopg2.errors.UniqueViolation) duplicate key value violates unique constraint "ix_trading_pairs_symbol"
DETAIL:  Key (symbol)=(BTC/USDT) already exists.

[SQL: INSERT INTO trading_pairs (id, symbol, base_asset, quote_asset, exchange, is_active, min_order_size, max_order_size, price_precision, quantity_precision) VALUES (%(id__0)s::UUID, %(symbol__0)s, %(base_asset__0)s, %(quote_asset__0)s, %(exchange__0)s,  ... 11613 characters truncated ... tity_precision__54)s) RETURNING trading_pairs.created_at, trading_pairs.updated_at, trading_pairs.id]
[parameters: {'price_precision__0': 8, 'id__0': UUID('43812e43-78ae-4763-83db-1eb351b51ed6'), 'is_active__0': True, 'min_order_size__0': None, 'base_asset__0': 'BTC', 'max_order_size__0': None, 'quantity_precision__0': 8, 'symbol__0': 'BTC/USDT', 'quote_asset__0': 'USDT', 'exchange__0': 'kraken', 'price_precision__1': 8, 'id__1': UUID('9fe6dd73-82e8-412b-a261-e011d6068bb6'), 'is_active__1': True, 'min_order_size__1': None, 'base_asset__1': 'ETH', 'max_order_size__1': None, 'quantity_precision__1': 8, 'symbol__1': 'ETH/USDT', 'quote_asset__1': 'USDT', 'exchange__1': 'kraken', 'price_precision__2': 8, 'id__2': UUID('de8eaff5-47dc-4e70-9020-d5ef602885f5'), 'is_active__2': True, 'min_order_size__2': None, 'base_asset__2': 'BNB', 'max_order_size__2': None, 'quantity_precision__2': 8, 'symbol__2': 'BNB/USDT', 'quote_asset__2': 'USDT', 'exchange__2': 'binance', 'price_precision__3': 8, 'id__3': UUID('a1a22f7f-111b-47ef-91d0-26c6cbdb2a19'), 'is_active__3': True, 'min_order_size__3': None, 'base_asset__3': 'BNB', 'max_order_size__3': None, 'quantity_precision__3': 8, 'symbol__3': 'BNB/USDT', 'quote_asset__3': 'USDT', 'exchange__3': 'kraken', 'price_precision__4': 8, 'id__4': UUID('05d80c19-d266-4876-aa81-da9fdef5456c'), 'is_active__4': True, 'min_order_size__4': None, 'base_asset__4': 'XRP', 'max_order_size__4': None, 'quantity_precision__4': 8, 'symbol__4': 'XRP/USDT', 'quote_asset__4': 'USDT', 'exchange__4': 'binance' ... 450 parameters truncated ... 'price_precision__50': 8, 'id__50': UUID('e36f1e3d-16c8-4817-9332-bdeb12e552ea'), 'is_active__50': True, 'min_order_size__50': None, 'base_asset__50': 'TRX', 'max_order_size__50': None, 'quantity_precision__50': 8, 'symbol__50': 'TRX/USDT', 'quote_asset__50': 'USDT', 'exchange__50': 'kraken', 'price_precision__51': 8, 'id__51': UUID('f0def20c-c7d5-48b7-80bb-992dde072a7e'), 'is_active__51': True, 'min_order_size__51': None, 'base_asset__51': 'EOS', 'max_order_size__51': None, 'quantity_precision__51': 8, 'symbol__51': 'EOS/USDT', 'quote_asset__51': 'USDT', 'exchange__51': 'binance', 'price_precision__52': 8, 'id__52': UUID('a7fa982f-67a7-4eae-bf53-8b51517477fd'), 'is_active__52': True, 'min_order_size__52': None, 'base_asset__52': 'EOS', 'max_order_size__52': None, 'quantity_precision__52': 8, 'symbol__52': 'EOS/USDT', 'quote_asset__52': 'USDT', 'exchange__52': 'kraken', 'price_precision__53': 8, 'id__53': UUID('a6b199d5-05d6-4350-a5bf-622717c9398b'), 'is_active__53': True, 'min_order_size__53': None, 'base_asset__53': 'IOTA', 'max_order_size__53': None, 'quantity_precision__53': 8, 'symbol__53': 'IOTA/USDT', 'quote_asset__53': 'USDT', 'exchange__53': 'binance', 'price_precision__54': 8, 'id__54': UUID('76ea066f-b1ce-445b-97c0-adbb6f3c606b'), 'is_active__54': True, 'min_order_size__54': None, 'base_asset__54': 'IOTA', 'max_order_size__54': None, 'quantity_precision__54': 8, 'symbol__54': 'IOTA/USDT', 'quote_asset__54': 'USDT', 'exchange__54': 'kraken'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-07 17:11:04,305 - src.data.market_data_collector - INFO - Market Data Collector initialized
2025-08-07 17:11:04,306 - src.core.ai_agent - INFO - Initializing AI Trading Agent...
2025-08-07 17:11:04,306 - src.ai.ai_manager - INFO - Initializing AI Manager...
2025-08-07 17:11:04,757 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 17:11:04,841 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openrouter
2025-08-07 17:11:04,842 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openrouter provider
2025-08-07 17:11:04,987 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 17:11:05,056 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openai
2025-08-07 17:11:05,057 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openai provider
2025-08-07 17:11:05,062 - src.core.ai_agent - INFO - AI Trading Agent initialized successfully
2025-08-07 17:11:05,063 - src.core.strategy_manager - INFO - Initializing Strategy Manager...
2025-08-07 17:11:05,066 - src.core.order_manager - INFO - Initializing Order Manager...
2025-08-07 17:11:05,069 - src.core.trading_engine - INFO - Trading Engine initialized successfully
2025-08-07 17:11:05,069 - src.core.trading_engine - INFO - Starting Trading Engine...
2025-08-07 17:11:05,074 - src.core.trading_engine - INFO - Trading Engine started successfully
2025-08-07 17:11:05,074 - __main__ - INFO - [OK] AI Trading Bot System started successfully!
2025-08-07 17:11:05,074 - src.data.market_data_collector - INFO - Starting collection loop for binance
2025-08-07 17:11:05,075 - src.data.market_data_collector - INFO - Starting collection loop for kraken
2025-08-07 17:11:05,075 - src.core.trading_engine - INFO - Starting main trading loop...
2025-08-07 17:11:05,466 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:05,466 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:05,467 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:05,595 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:06,733 - src.exchanges.exchange_manager - ERROR - Failed to get ticker XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:11:06,734 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:11:06,734 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:11:06,734 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:11:06,734 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:11:06,735 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:11:06,735 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XBT/USDT from kraken: kraken does not have market symbol XBT/USDT
2025-08-07 17:11:07,080 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:08,257 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:08,257 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:08,258 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:08,387 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:08,914 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:11:09,853 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:10,210 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:11:11,042 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:11,042 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:11,043 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:11,175 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:11,301 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:11:12,524 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:11:12,646 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:13,698 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:11:13,844 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:13,845 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:13,845 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:13,966 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:14,907 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 17:11:15,430 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:16,629 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:16,629 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:16,629 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:16,629 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:16,756 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:17,330 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:11:18,227 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:18,480 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:11:19,418 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:19,418 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:19,419 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:19,419 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:19,534 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:19,644 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:11:20,821 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:11:21,014 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:21,993 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:11:22,212 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:22,213 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:22,213 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:22,213 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:22,321 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:23,133 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 17:11:23,802 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:24,985 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:24,985 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:24,986 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:24,986 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 17:11:24,986 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:25,119 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BNB/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:25,535 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 17:11:26,583 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:26,828 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 17:11:27,773 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:27,773 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:27,773 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:27,774 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 17:11:27,774 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:27,910 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:27,938 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 17:11:29,145 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 17:11:29,375 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:30,333 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 17:11:30,574 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:30,575 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:30,576 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:30,576 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 17:11:30,576 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:30,689 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:31,593 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 17:11:32,167 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:33,461 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:33,461 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:33,461 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:33,461 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 17:11:33,462 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 17:11:33,462 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:33,478 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:33,846 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 17:11:34,957 - src.exchanges.exchange_manager - ERROR - Failed to get ticker XRP/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:35,008 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 17:11:36,137 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:36,137 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:36,137 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:36,138 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 17:11:36,138 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 17:11:36,138 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:36,187 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 17:11:36,261 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:37,355 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 17:11:37,734 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
2025-08-07 17:11:38,703 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 17:11:38,931 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 17:11:38,931 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 17:11:38,931 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 17:11:38,932 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 17:11:38,932 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 17:11:38,932 - src.portfolio.portfolio_manager - ERROR - Failed to calculate total portfolio value: 'TradingSettings' object has no attribute 'get'
2025-08-07 17:11:39,054 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-1021,"msg":"Timestamp for this request was 1000ms ahead of the server's time."}
