"""
Portfolio Management Module for AI Trading Bot System.

This module provides portfolio management capabilities including
position tracking, performance analysis, and rebalancing.

Author: inkbytefo
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..config.settings import Settings


@dataclass
class Position:
    """Represents a trading position."""
    symbol: str
    quantity: float
    entry_price: float
    current_price: float
    entry_time: datetime
    side: str  # 'long' or 'short'
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0


@dataclass
class PortfolioSnapshot:
    """Portfolio snapshot at a point in time."""
    timestamp: datetime
    total_value: float
    cash_balance: float
    positions_value: float
    unrealized_pnl: float
    realized_pnl: float
    position_count: int


class PortfolioManager:
    """
    Portfolio management system for tracking positions and performance.
    
    Manages portfolio positions, calculates performance metrics,
    and provides portfolio analysis capabilities.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Portfolio state
        self.cash_balance = 10000.0  # Starting balance
        self.positions: Dict[str, Position] = {}
        self.portfolio_history: List[PortfolioSnapshot] = []
        
        # Performance tracking
        self.total_realized_pnl = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        
    def add_position(self, symbol: str, quantity: float, price: float, 
                    side: str = 'long') -> bool:
        """
        Add a new position to the portfolio.
        
        Args:
            symbol: Trading pair symbol
            quantity: Position quantity
            price: Entry price
            side: Position side ('long' or 'short')
            
        Returns:
            True if position added successfully
        """
        try:
            position_value = quantity * price
            
            # Check if we have enough cash
            if position_value > self.cash_balance:
                self.logger.warning(f"Insufficient cash for position: {position_value} > {self.cash_balance}")
                return False
            
            # If position already exists, update it
            if symbol in self.positions:
                existing_pos = self.positions[symbol]
                
                # Calculate new average price
                total_quantity = existing_pos.quantity + quantity
                total_value = (existing_pos.quantity * existing_pos.entry_price) + position_value
                new_avg_price = total_value / total_quantity
                
                # Update position
                existing_pos.quantity = total_quantity
                existing_pos.entry_price = new_avg_price
                
                self.logger.info(f"Updated position {symbol}: {total_quantity} @ {new_avg_price}")
            else:
                # Create new position
                position = Position(
                    symbol=symbol,
                    quantity=quantity,
                    entry_price=price,
                    current_price=price,
                    entry_time=datetime.utcnow(),
                    side=side
                )
                self.positions[symbol] = position
                
                self.logger.info(f"Added new position {symbol}: {quantity} @ {price}")
            
            # Update cash balance
            self.cash_balance -= position_value
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add position: {e}")
            return False
    
    def close_position(self, symbol: str, quantity: Optional[float] = None, 
                      price: Optional[float] = None) -> Dict[str, Any]:
        """
        Close a position (fully or partially).
        
        Args:
            symbol: Trading pair symbol
            quantity: Quantity to close (None for full position)
            price: Exit price (None to use current price)
            
        Returns:
            Dictionary with closing details
        """
        try:
            if symbol not in self.positions:
                return {"success": False, "error": "Position not found"}
            
            position = self.positions[symbol]
            
            # Use full quantity if not specified
            if quantity is None:
                quantity = position.quantity
            
            # Use current price if not specified
            if price is None:
                price = position.current_price
            
            # Validate quantity
            if quantity > position.quantity:
                return {"success": False, "error": "Insufficient position size"}
            
            # Calculate P&L
            if position.side == 'long':
                pnl = quantity * (price - position.entry_price)
            else:  # short
                pnl = quantity * (position.entry_price - price)
            
            # Update cash balance
            self.cash_balance += quantity * price
            
            # Update position
            if quantity == position.quantity:
                # Full close
                del self.positions[symbol]
                self.logger.info(f"Closed full position {symbol}: P&L {pnl:.2f}")
            else:
                # Partial close
                position.quantity -= quantity
                self.logger.info(f"Partially closed position {symbol}: {quantity} units, P&L {pnl:.2f}")
            
            # Update performance tracking
            self.total_realized_pnl += pnl
            self.total_trades += 1
            
            if pnl > 0:
                self.winning_trades += 1
            else:
                self.losing_trades += 1
            
            return {
                "success": True,
                "symbol": symbol,
                "quantity": quantity,
                "price": price,
                "pnl": pnl,
                "remaining_quantity": position.quantity if symbol in self.positions else 0
            }
            
        except Exception as e:
            self.logger.error(f"Failed to close position: {e}")
            return {"success": False, "error": str(e)}
    
    def update_prices(self, price_data: Dict[str, float]) -> None:
        """
        Update current prices for all positions.
        
        Args:
            price_data: Dictionary of symbol -> current price
        """
        try:
            for symbol, position in self.positions.items():
                if symbol in price_data:
                    position.current_price = price_data[symbol]
                    
                    # Calculate unrealized P&L
                    if position.side == 'long':
                        position.unrealized_pnl = position.quantity * (position.current_price - position.entry_price)
                    else:  # short
                        position.unrealized_pnl = position.quantity * (position.entry_price - position.current_price)
            
        except Exception as e:
            self.logger.error(f"Failed to update prices: {e}")
    
    def get_portfolio_value(self) -> float:
        """Calculate total portfolio value."""
        try:
            positions_value = sum(
                pos.quantity * pos.current_price 
                for pos in self.positions.values()
            )
            return self.cash_balance + positions_value
            
        except Exception as e:
            self.logger.error(f"Failed to calculate portfolio value: {e}")
            return self.cash_balance
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get comprehensive portfolio summary."""
        try:
            total_value = self.get_portfolio_value()
            positions_value = sum(pos.quantity * pos.current_price for pos in self.positions.values())
            total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
            
            # Calculate performance metrics
            win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
            
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "total_value": total_value,
                "cash_balance": self.cash_balance,
                "positions_value": positions_value,
                "cash_percentage": (self.cash_balance / total_value * 100) if total_value > 0 else 100,
                "positions_percentage": (positions_value / total_value * 100) if total_value > 0 else 0,
                "unrealized_pnl": total_unrealized_pnl,
                "realized_pnl": self.total_realized_pnl,
                "total_pnl": total_unrealized_pnl + self.total_realized_pnl,
                "position_count": len(self.positions),
                "performance": {
                    "total_trades": self.total_trades,
                    "winning_trades": self.winning_trades,
                    "losing_trades": self.losing_trades,
                    "win_rate": win_rate,
                    "total_return": ((total_value - 10000) / 10000 * 100) if total_value > 0 else 0
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get portfolio summary: {e}")
            return {}
    
    def get_position_details(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        Get detailed position information.
        
        Args:
            symbol: Specific symbol (None for all positions)
            
        Returns:
            Position details
        """
        try:
            if symbol:
                if symbol not in self.positions:
                    return {"error": "Position not found"}
                
                pos = self.positions[symbol]
                return {
                    "symbol": pos.symbol,
                    "quantity": pos.quantity,
                    "entry_price": pos.entry_price,
                    "current_price": pos.current_price,
                    "entry_time": pos.entry_time.isoformat(),
                    "side": pos.side,
                    "unrealized_pnl": pos.unrealized_pnl,
                    "unrealized_pnl_percentage": (pos.unrealized_pnl / (pos.quantity * pos.entry_price) * 100),
                    "position_value": pos.quantity * pos.current_price,
                    "days_held": (datetime.utcnow() - pos.entry_time).days
                }
            else:
                # Return all positions
                positions_data = {}
                for symbol, pos in self.positions.items():
                    positions_data[symbol] = {
                        "quantity": pos.quantity,
                        "entry_price": pos.entry_price,
                        "current_price": pos.current_price,
                        "unrealized_pnl": pos.unrealized_pnl,
                        "position_value": pos.quantity * pos.current_price,
                        "side": pos.side
                    }
                return positions_data
                
        except Exception as e:
            self.logger.error(f"Failed to get position details: {e}")
            return {"error": str(e)}
    
    def take_snapshot(self) -> None:
        """Take a portfolio snapshot for historical tracking."""
        try:
            total_value = self.get_portfolio_value()
            positions_value = sum(pos.quantity * pos.current_price for pos in self.positions.values())
            total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
            
            snapshot = PortfolioSnapshot(
                timestamp=datetime.utcnow(),
                total_value=total_value,
                cash_balance=self.cash_balance,
                positions_value=positions_value,
                unrealized_pnl=total_unrealized_pnl,
                realized_pnl=self.total_realized_pnl,
                position_count=len(self.positions)
            )
            
            self.portfolio_history.append(snapshot)
            
            # Keep only last 1000 snapshots
            if len(self.portfolio_history) > 1000:
                self.portfolio_history = self.portfolio_history[-1000:]
                
        except Exception as e:
            self.logger.error(f"Failed to take portfolio snapshot: {e}")
    
    def get_performance_metrics(self, days: int = 30) -> Dict[str, Any]:
        """
        Calculate performance metrics over specified period.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Performance metrics
        """
        try:
            if not self.portfolio_history:
                return {"error": "No historical data available"}
            
            # Filter snapshots by date
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            recent_snapshots = [
                s for s in self.portfolio_history 
                if s.timestamp >= cutoff_date
            ]
            
            if len(recent_snapshots) < 2:
                return {"error": "Insufficient historical data"}
            
            # Calculate metrics
            start_value = recent_snapshots[0].total_value
            end_value = recent_snapshots[-1].total_value
            
            total_return = ((end_value - start_value) / start_value * 100) if start_value > 0 else 0
            
            # Calculate daily returns for volatility
            daily_returns = []
            for i in range(1, len(recent_snapshots)):
                prev_value = recent_snapshots[i-1].total_value
                curr_value = recent_snapshots[i].total_value
                daily_return = ((curr_value - prev_value) / prev_value) if prev_value > 0 else 0
                daily_returns.append(daily_return)
            
            # Calculate volatility (standard deviation of returns)
            if daily_returns:
                avg_return = sum(daily_returns) / len(daily_returns)
                variance = sum((r - avg_return) ** 2 for r in daily_returns) / len(daily_returns)
                volatility = variance ** 0.5 * (252 ** 0.5)  # Annualized
            else:
                volatility = 0
            
            # Calculate max drawdown
            peak_value = start_value
            max_drawdown = 0
            for snapshot in recent_snapshots:
                if snapshot.total_value > peak_value:
                    peak_value = snapshot.total_value
                drawdown = (peak_value - snapshot.total_value) / peak_value
                max_drawdown = max(max_drawdown, drawdown)
            
            return {
                "period_days": days,
                "start_value": start_value,
                "end_value": end_value,
                "total_return": total_return,
                "annualized_return": (total_return / days * 365) if days > 0 else 0,
                "volatility": volatility * 100,  # Convert to percentage
                "max_drawdown": max_drawdown * 100,  # Convert to percentage
                "sharpe_ratio": (total_return / volatility) if volatility > 0 else 0,
                "total_trades": self.total_trades,
                "win_rate": (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"Failed to calculate performance metrics: {e}")
            return {"error": str(e)}

    async def health_check(self) -> bool:
        """
        Perform health check for Portfolio Manager.

        Returns:
            bool: True if healthy, False otherwise
        """
        try:
            # Check if portfolio manager is properly initialized
            if not hasattr(self, 'settings'):
                self.logger.error("Portfolio Manager not properly initialized")
                return False

            # Check if we have valid portfolio data
            if not hasattr(self, 'portfolio'):
                self.logger.warning("Portfolio data not initialized")
                return True  # This is OK for startup

            # Check portfolio balance consistency
            total_balance = sum(self.portfolio.values())
            if total_balance < 0:
                self.logger.error(f"Negative total portfolio balance: {total_balance}")
                return False

            # Check for any critical errors in recent operations
            # This is a basic health check - can be expanded

            self.logger.debug("Portfolio Manager health check passed")
            return True

        except Exception as e:
            self.logger.error(f"Portfolio Manager health check failed: {e}")
            return False

    def get_total_value(self, prices: Dict[str, float] = None) -> float:
        """
        Calculate total portfolio value in base currency (usually USDT).

        Args:
            prices: Optional dict of current prices for assets

        Returns:
            float: Total portfolio value in base currency
        """
        try:
            if not hasattr(self, 'portfolio') or not self.portfolio:
                self.logger.warning("Portfolio not initialized, returning 0")
                return 0.0

            total_value = 0.0
            base_currency = self.settings.trading.get('base_currency', 'USDT')

            for asset, balance in self.portfolio.items():
                if balance <= 0:
                    continue

                if asset == base_currency:
                    # Base currency value is 1:1
                    total_value += balance
                else:
                    # Need to convert to base currency
                    if prices and f"{asset}/{base_currency}" in prices:
                        price = prices[f"{asset}/{base_currency}"]
                        total_value += balance * price
                    elif prices and f"{asset}USDT" in prices:
                        # Try alternative format
                        price = prices[f"{asset}USDT"]
                        total_value += balance * price
                    else:
                        # If no price available, try to estimate or skip
                        self.logger.warning(f"No price available for {asset}, skipping in total value calculation")
                        continue

            self.logger.debug(f"Total portfolio value: {total_value} {base_currency}")
            return round(total_value, 8)

        except Exception as e:
            self.logger.error(f"Failed to calculate total portfolio value: {e}")
            return 0.0

    def get_total_value_usd(self, prices: Dict[str, float] = None) -> float:
        """
        Calculate total portfolio value in USD.

        Args:
            prices: Optional dict of current prices for assets

        Returns:
            float: Total portfolio value in USD
        """
        try:
            # First get value in base currency
            base_value = self.get_total_value(prices)
            base_currency = self.settings.trading.get('base_currency', 'USDT')

            if base_currency == 'USD' or base_currency == 'USDT':
                # Already in USD equivalent
                return base_value

            # Convert to USD if needed
            if prices and f"{base_currency}/USD" in prices:
                usd_rate = prices[f"{base_currency}/USD"]
                return base_value * usd_rate
            elif prices and f"{base_currency}USD" in prices:
                usd_rate = prices[f"{base_currency}USD"]
                return base_value * usd_rate
            else:
                # Assume 1:1 with USD if no conversion rate available
                self.logger.warning(f"No USD conversion rate for {base_currency}, assuming 1:1")
                return base_value

        except Exception as e:
            self.logger.error(f"Failed to calculate USD portfolio value: {e}")
            return 0.0
