#!/usr/bin/env python3
"""
Hızlı Binance API Test
API anahtarı düzeltmelerinden sonra hızlı test için.
"""

import ccxt
import asyncio
from dotenv import load_dotenv
import os

load_dotenv()

async def quick_test():
    api_key = os.getenv('BINANCE_API_KEY')
    secret_key = os.getenv('BINANCE_SECRET_KEY')
    
    print(f"🔑 API Key: {api_key[:10]}...{api_key[-10:]}")
    print("🧪 Hızlı test başlatılıyor...")
    
    try:
        exchange = ccxt.binance({
            'apiKey': api_key,
            'secret': secret_key,
            'sandbox': False,
            'enableRateLimit': True,
            'options': {
                'adjustForTimeDifference': True,  # Zaman farkını otomatik ayarla
                'recvWindow': 60000,  # Zaman toleransını çok artır (60 saniye)
                'timeDifference': 0,  # <PERSON>aman farkı ayarı
            }
        })
        
        # Sadece hesap bilgilerini al
        balance = await exchange.fetch_balance()
        print("✅ API anahtarı çalışıyor!")
        print(f"📊 Hesap bilgileri alındı: {len(balance)} varlık")
        
        await exchange.close()
        return True
        
    except Exception as e:
        print(f"❌ Hata: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(quick_test())
    if success:
        print("\n🎉 API anahtarı hazır! Trading bot'u çalıştırabilirsiniz.")
    else:
        print("\n💥 API anahtarı hala çalışmıyor. Binance ayarlarını kontrol edin.")
