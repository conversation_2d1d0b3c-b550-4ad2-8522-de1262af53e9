#!/usr/bin/env python3
"""
Hızlı Binance API Test
API anahtarı düzeltmelerinden sonra hızlı test için.
"""

import ccxt
import asyncio
from dotenv import load_dotenv
import os

load_dotenv()

async def quick_test():
    api_key = os.getenv('BINANCE_API_KEY')
    secret_key = os.getenv('BINANCE_SECRET_KEY')
    
    print(f"[KEY] API Key: {api_key[:10]}...{api_key[-10:]}")
    print("[TEST] Hızlı test başlatılıyor...")
    
    try:
        exchange = ccxt.binance({
            'apiKey': api_key,
            'secret': secret_key,
            'sandbox': False,
            'enableRateLimit': True,
            'options': {
                'adjustForTimeDifference': True,  # Zaman farkını otomatik ayarla
                'recvWindow': 60000,  # <PERSON>aman toleransını çok artır (60 saniye)
                'timeDifference': 0,  # <PERSON> zaman farkı ayarı
            }
        })
        
        # Sadece hesap bilgilerini al
        balance = await exchange.fetch_balance()
        print("[OK] API anahtarı çalışıyor!")
        print(f"[INFO] Hesap bilgileri alındı: {len(balance)} varlık")
        
        await exchange.close()
        return True
        
    except Exception as e:
        print(f"[ERROR] Hata: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(quick_test())
    if success:
        print("\n[SUCCESS] API anahtarı hazır! Trading bot'u çalıştırabilirsiniz.")
    else:
        print("\n[FAIL] API anahtarı hala çalışmıyor. Binance ayarlarını kontrol edin.")
