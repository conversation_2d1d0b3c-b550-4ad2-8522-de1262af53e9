"""
Main Trading Engine for AI Trading Bot System.

This module contains the core trading engine that orchestrates all trading
activities, manages AI agents, and coordinates between different components.

Author: inkbytefo
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..config.settings import Settings
from ..data.market_data_collector import MarketDataCollector
from ..analysis.technical_analyzer import TechnicalAnalyzer
from ..analysis.sentiment_analyzer import SentimentAnalyzer
from ..risk.risk_manager import RiskManager
from ..portfolio.portfolio_manager import PortfolioManager
from ..exchanges.exchange_manager import ExchangeManager
from .ai_agent import AITradingAgent
from .strategy_manager import StrategyManager
from .order_manager import OrderManager


class TradingEngine:
    """
    Main trading engine that coordinates all trading activities.
    
    This class serves as the central orchestrator for the AI trading bot,
    managing data collection, analysis, decision making, and order execution.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Core components
        self.market_data_collector: Optional[MarketDataCollector] = None
        self.technical_analyzer: Optional[TechnicalAnalyzer] = None
        self.sentiment_analyzer: Optional[SentimentAnalyzer] = None
        self.risk_manager: Optional[RiskManager] = None
        self.portfolio_manager: Optional[PortfolioManager] = None
        self.exchange_manager: Optional[ExchangeManager] = None
        self.ai_agent: Optional[AITradingAgent] = None
        self.strategy_manager: Optional[StrategyManager] = None
        self.order_manager: Optional[OrderManager] = None
        
        # State management
        self.is_running = False
        self.is_initialized = False
        self.last_update = None
        self.trading_pairs = settings.trading.default_trading_pairs
        
        # Performance metrics
        self.metrics = {
            "total_trades": 0,
            "successful_trades": 0,
            "failed_trades": 0,
            "total_profit_loss": 0.0,
            "uptime": 0,
            "last_trade_time": None
        }
    
    async def initialize(self):
        """Initialize all components of the trading engine."""
        try:
            self.logger.info("Initializing Trading Engine components...")
            
            # Initialize exchange manager first
            self.exchange_manager = ExchangeManager(self.settings)
            await self.exchange_manager.initialize()
            
            # Initialize data collection
            self.market_data_collector = MarketDataCollector(
                self.settings, self.exchange_manager
            )
            await self.market_data_collector.initialize()
            
            # Initialize analysis components
            self.technical_analyzer = TechnicalAnalyzer(self.settings)
            self.sentiment_analyzer = SentimentAnalyzer(self.settings)
            
            # Initialize risk and portfolio management
            self.risk_manager = RiskManager(self.settings)
            self.portfolio_manager = PortfolioManager(self.settings)
            
            # Initialize AI agent
            self.ai_agent = AITradingAgent(self.settings)
            await self.ai_agent.initialize()
            
            # Initialize strategy and order management
            self.strategy_manager = StrategyManager(self.settings)
            await self.strategy_manager.initialize()

            self.order_manager = OrderManager(self.settings)
            await self.order_manager.initialize()
            
            self.is_initialized = True
            self.logger.info("Trading Engine initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize Trading Engine: {e}")
            raise
    
    async def start(self):
        """Start the trading engine and begin trading operations."""
        if not self.is_initialized:
            raise RuntimeError("Trading Engine must be initialized before starting")
        
        try:
            self.logger.info("Starting Trading Engine...")

            # Start all components
            await self.market_data_collector.start()

            self.is_running = True
            self.last_update = datetime.utcnow()

            # Start main trading loop
            asyncio.create_task(self._main_trading_loop())

            self.logger.info("Trading Engine started successfully")

        except Exception as e:
            self.logger.error(f"Failed to start Trading Engine: {e}")
            raise
    
    async def stop(self):
        """Stop the trading engine and all trading operations."""
        try:
            self.logger.info("Stopping Trading Engine...")

            self.is_running = False

            # Stop all components
            if self.market_data_collector:
                await self.market_data_collector.stop()

            if self.ai_agent:
                await self.ai_agent.stop()

            if self.order_manager:
                await self.order_manager.cancel_all_orders()

            self.logger.info("Trading Engine stopped successfully")

        except Exception as e:
            self.logger.error(f"Error stopping Trading Engine: {e}")
    
    async def _main_trading_loop(self):
        """Main trading loop that runs continuously."""
        self.logger.info("Starting main trading loop...")
        
        while self.is_running:
            try:
                # Update timestamp
                self.last_update = datetime.utcnow()
                
                # Check system health
                if not await self._health_check():
                    self.logger.warning("Health check failed, skipping trading cycle")
                    await asyncio.sleep(10)
                    continue
                
                # Get latest market data
                market_data = await self.market_data_collector.get_latest_data()
                
                # Perform analysis
                analysis_results = await self._perform_analysis(market_data)
                
                # Make trading decisions
                trading_decisions = await self.ai_agent.make_decisions(
                    market_data, analysis_results
                )
                
                # Execute trades
                if trading_decisions:
                    # Veri tipini kontrol et
                    if isinstance(trading_decisions, dict) and "decisions" in trading_decisions:
                        decisions_list = trading_decisions["decisions"]
                    elif isinstance(trading_decisions, list):
                        decisions_list = trading_decisions
                    else:
                        self.logger.error(f"Invalid trading decisions format: {type(trading_decisions)}")
                        decisions_list = []

                    if decisions_list:
                        await self._execute_trading_decisions(decisions_list)
                
                # Update portfolio and metrics
                await self._update_metrics()
                
                # Sleep before next cycle
                await asyncio.sleep(1)  # 1 second cycle
                
            except Exception as e:
                self.logger.error(f"Error in main trading loop: {e}")
                await asyncio.sleep(5)  # Wait before retrying
    
    async def _health_check(self) -> bool:
        """Perform system health check."""
        try:
            # Check exchange connectivity
            if not await self.exchange_manager.health_check():
                return False
            
            # Check risk limits
            if not await self.risk_manager.check_risk_limits():
                return False
            
            # Check portfolio status
            if not await self.portfolio_manager.health_check():
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Health check error: {e}")
            return False
    
    async def _perform_analysis(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform technical and sentiment analysis."""
        try:
            # Initialize analysis results
            analysis_results = {
                "technical": {},
                "sentiment": {},
                "timestamp": datetime.utcnow()
            }

            # Technical analysis for each symbol
            if market_data and "data" in market_data:
                technical_results = {}

                for symbol_key, symbol_data in market_data["data"].items():
                    try:
                        # Extract symbol from key (e.g., "binance:BTC/USDT" -> "BTC/USDT")
                        symbol = symbol_data.get("symbol", symbol_key.split(":")[-1] if ":" in symbol_key else symbol_key)

                        # Skip if no ticker data available
                        if not symbol_data.get("ticker"):
                            self.logger.debug(f"No ticker data for {symbol}, skipping technical analysis")
                            continue

                        # For now, use basic analysis since we don't have OHLCV data
                        # In a real implementation, you would fetch OHLCV data here
                        technical_result = await self.technical_analyzer.analyze(symbol)

                        if isinstance(technical_result, dict):
                            technical_results[symbol] = technical_result
                        else:
                            self.logger.warning(f"Technical analysis returned non-dict for {symbol}: {type(technical_result)}")

                    except Exception as e:
                        self.logger.error(f"Technical analysis failed for {symbol_key}: {e}")
                        continue

                analysis_results["technical"] = technical_results

            # Sentiment analysis
            try:
                sentiment_data = await self.sentiment_analyzer.get_latest_sentiment()
                analysis_results["sentiment"] = sentiment_data if isinstance(sentiment_data, dict) else {}
            except Exception as e:
                self.logger.error(f"Sentiment analysis failed: {e}")
                analysis_results["sentiment"] = {}

            return analysis_results

        except Exception as e:
            self.logger.error(f"Analysis error: {e}")
            return {
                "technical": {},
                "sentiment": {},
                "timestamp": datetime.utcnow()
            }
    
    async def _execute_trading_decisions(self, decisions: List[Dict[str, Any]]):
        """Execute trading decisions made by the AI agent."""
        try:
            for decision in decisions:
                try:
                    # Extract required parameters for risk validation
                    symbol = decision.get("symbol", "UNKNOWN")
                    side = decision.get("decision", "hold")  # buy/sell/hold
                    price = decision.get("price", 0.0)

                    # Skip if not a buy/sell decision
                    if side not in ["buy", "sell"]:
                        continue

                    # Get portfolio value for risk calculation
                    portfolio_value = 10000.0  # Default value, should be from portfolio manager
                    try:
                        portfolio_value = self.portfolio_manager.get_total_value()
                    except Exception as e:
                        self.logger.warning(f"Could not get portfolio value, using default: {e}")

                    # Calculate quantity based on risk management
                    quantity = self.risk_manager.calculate_position_size(
                        symbol=symbol,
                        price=price,
                        portfolio_value=portfolio_value
                    )

                    if quantity <= 0:
                        self.logger.warning(f"Invalid quantity calculated for {symbol}: {quantity}")
                        continue

                    # Validate decision with risk manager
                    validation_result = self.risk_manager.validate_trade(
                        symbol=symbol,
                        side=side,
                        quantity=quantity,
                        price=price,
                        portfolio_value=portfolio_value
                    )

                    if validation_result.get("valid", False):
                        # Prepare order for execution
                        order_data = {
                            "symbol": symbol,
                            "side": side,
                            "quantity": quantity,
                            "price": price,
                            "type": "market",  # or "limit"
                            "decision": decision
                        }

                        # Execute the trade
                        result = await self.order_manager.execute_order(order_data)

                        if result.get("success", False):
                            self.metrics["successful_trades"] += 1
                            self.metrics["last_trade_time"] = datetime.utcnow()
                            self.logger.info(f"Trade executed successfully: {symbol} {side} {quantity}")
                        else:
                            self.metrics["failed_trades"] += 1
                            self.logger.error(f"Trade execution failed: {result.get('error', 'Unknown error')}")

                        self.metrics["total_trades"] += 1
                    else:
                        self.logger.warning(f"Trade rejected by risk manager: {validation_result.get('reason', 'Unknown reason')}")

                except Exception as e:
                    self.logger.error(f"Error processing trading decision: {e}")
                    self.metrics["failed_trades"] += 1

        except Exception as e:
            self.logger.error(f"Trade execution error: {e}")
    
    async def _update_metrics(self):
        """Update performance metrics."""
        try:
            # Update portfolio metrics
            portfolio_value = self.portfolio_manager.get_total_value()

            # Calculate P&L
            # This would be implemented based on initial portfolio value

            # Update uptime
            if self.last_update:
                uptime_delta = datetime.utcnow() - self.last_update
                self.metrics["uptime"] += uptime_delta.total_seconds()

        except Exception as e:
            self.logger.error(f"Metrics update error: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the trading engine."""
        return {
            "is_running": self.is_running,
            "is_initialized": self.is_initialized,
            "last_update": self.last_update,
            "trading_pairs": self.trading_pairs,
            "metrics": self.metrics,
            "paper_trading": self.settings.PAPER_TRADING
        }
    
    async def emergency_stop(self):
        """Emergency stop all trading activities."""
        self.logger.critical("EMERGENCY STOP ACTIVATED!")

        try:
            # Cancel all open orders
            await self.order_manager.cancel_all_orders()

            # Close all positions if configured
            if self.settings.risk.emergency_stop_loss:
                await self.order_manager.close_all_positions()

            # Stop the engine
            await self.stop()

            self.logger.critical("Emergency stop completed")

        except Exception as e:
            self.logger.critical(f"Emergency stop error: {e}")
